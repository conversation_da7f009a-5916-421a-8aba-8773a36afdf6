### **JobPlus - 前端统一样式规范 V1.0**

本文档为 AI 编程工具 Augment 提供前端开发的视觉与代码样式规范。所有前端代码的生成与修改，必须严格遵守此规范。

#### **0. 指导原则**

1.  **清晰聚焦 (Clarity & Focus)**：UI 设计的首要任务是引导用户完成核心任务（如面试练习），避免不必要的视觉干扰。界面元素应简洁、直观。
2.  **专业信赖 (Professional & Trustworthy)**：作为一款求职辅助工具，视觉上应体现专业性和可靠性。采用稳重的色彩和清晰的排版。
3.  **温暖鼓励 (Warm & Encouraging)**：通过柔和的色彩、圆角和友好的交互反馈，营造一个支持性和无压力的使用环境。
4.  **响应式与可访问性 (Responsive & Accessible)**：确保在所有主流设备（桌面、平板、手机）上都有良好的体验，并遵循 WCAG 2.1 AA 标准，服务所有用户。

#### **1. 设计系统与设计令牌 (Design Tokens)**

所有样式变量都将通过 **Tailwind CSS** 的配置文件 `tailwind.config.js`进行统一管理。**严禁在代码中使用魔术数字或硬编码的颜色值。**

##### **1.1 色彩 (Colors)**

色彩体系旨在营造专业而温暖的感觉。

*   **主色 (Primary)**: 蓝色系，代表专业、沟通和科技。
*   **中性色 (Neutral)**: 灰色系，用于背景、边框、正文和辅助文本。
*   **功能色 (Functional)**: 用于表示成功、警告、危险和信息状态。

**`tailwind.config.js` 配置指令:**

```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))", // 边框
        input: "hsl(var(--input))",   // 输入框边框
        ring: "hsl(var(--ring))",     // 焦点环

        background: "hsl(var(--background))", // 页面背景
        foreground: "hsl(var(--foreground))", // 主要文字

        primary: {
          DEFAULT: "hsl(var(--primary))",       // 主色
          foreground: "hsl(var(--primary-foreground))", // 主色上的文字
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",     // 次要色/组件背景
          foreground: "hsl(var(--secondary-foreground))", // 次要色上的文字
        },

        destructive: {
          DEFAULT: "hsl(var(--destructive))", // 危险/删除
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",       // 静默/卡片背景
          foreground: "hsl(var(--muted-foreground))", // 辅助性/静默文字
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",       // 悬停等强调背景
          foreground: "hsl(var(--accent-foreground))",
        },
        
        // 新增的功能色，用于特定场景如图标、标签
        success: {
          DEFAULT: 'hsl(142.1 76.2% 41.2%)', // 绿色
          foreground: 'hsl(142.1 76.2% 10%)',
        },
        warning: {
          DEFAULT: 'hsl(47.9 95.8% 53.1%)', // 黄色
          foreground: 'hsl(47.9 95.8% 15%)',
        },
      },
      // ...
    },
  },
  // ...
};
```

**`src/styles/globals.css` 变量定义:**

```css
/* src/styles/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }
}
```

##### **1.2 字体 (Typography)**

*   **主字体 (Sans-serif)**: `Inter`。现代、清晰、可读性高。通过 `index.html` 从 Google Fonts 引入。
*   **等宽字体 (Monospace)**: `JetBrains Mono`。用于展示代码或需要对齐的文本。
*   **字号体系**: 使用 `rem` 单位，基于 16px 根字体大小。

**`tailwind.config.js` 配置指令:**

```javascript
// tailwind.config.js
const { fontFamily } = require("tailwindcss/defaultTheme");

module.exports = {
  theme: {
    extend: {
      fontFamily: {
        sans: ["Inter", ...fontFamily.sans],
        mono: ["JetBrains Mono", ...fontFamily.mono],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],    // 12px
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],// 14px
        'base': ['1rem', { lineHeight: '1.5rem' }],    // 16px
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],// 18px
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],// 20px
        '2xl': ['1.5rem', { lineHeight: '2rem' }],    // 24px
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],// 30px
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],  // 36px
      },
    },
  },
};
```

##### **1.3 间距与尺寸 (Spacing & Sizing)**

*   **必须** 使用 Tailwind 的默认间距比例（4px 基础单位，如 `p-1`=4px, `p-2`=8px）。
*   组件与组件、元素与元素之间的间距，应为 4px 的倍数。

##### **1.4 圆角 (Border Radius)**

*   **`rounded-md` (0.375rem / 6px)**: 主要用于按钮、输入框、卡片等大多数组件。
*   **`rounded-lg` (0.5rem / 8px)**: 用于较大面板或模态框。
*   **`rounded-full`**: 用于头像、圆形按钮等。

**`tailwind.config.js` 配置指令:**

```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      borderRadius: {
        lg: `var(--radius)`,
        md: `calc(var(--radius) - 2px)`,
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
};
```

##### **1.5 阴影 (Box Shadow)**

使用柔和的阴影来创造层次感。

*   `shadow-sm`: 用于轻微的悬浮效果，如 hover 状态。
*   `shadow-md`: 用于卡片、弹出菜单等默认的静态阴影。
*   `shadow-lg`: 用于模态框等需要更强层级区分的元素。

#### **2. 布局 (Layout)**

1.  **全局布局**:
    *   采用经典的 `Header` + `Main` 布局结构。
    *   `Main` 区域的内容应被一个容器包裹，以限制最大宽度并提供左右内边距。
    *   **指令**: 创建 `Layout.tsx` 组件，所有页面级组件都应被其包裹。
2.  **容器 (Container)**:
    *   **必须** 使用一个统一的容器样式。
    *   **指令**: `.container` 类应为 `w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8`。
3.  **响应式设计**:
    *   **必须** 采用移动端优先的原则进行开发。
    *   **必须** 使用 Tailwind 的响应式前缀 (`sm:`, `md:`, `lg:`, `xl:`) 来适配不同屏幕尺寸。

#### **3. 组件样式**

1.  **组件库**:
    *   **必须** 优先使用 **Shadcn/ui** 作为基础组件库。AI 应通过 CLI `npx shadcn-ui@latest add [component]` 来添加组件。
    *   Shadcn/ui 的组件是无样式的，它们的视觉表现完全由 `globals.css` 和 `tailwind.config.js` 中的设计令牌决定。
2.  **自定义组件**:
    *   当需要创建 Shadcn/ui 中没有的自定义组件（如 `ChatBubble.tsx`）时，**必须** 使用 Tailwind 的工具类进行从零构建。
    *   **严禁** 为组件编写单独的 `.css` 文件或使用 CSS-in-JS 库。所有样式都通过 `className` prop 传入。
3.  **交互状态**:
    *   所有可交互元素（按钮、链接、输入框）**必须** 有清晰的 `hover`, `focus`, `active`, `disabled` 状态。
    *   **`hover`**: 通常是背景色变亮或变暗（如 `hover:bg-primary/90`）。
    *   **`focus`**: **必须** 使用 `focus-visible` 来添加一个清晰的 `ring`（焦点环），如 `focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2`。
    *   **`disabled`**: **必须** 有明确的禁用样式，如 `disabled:opacity-50 disabled:cursor-not-allowed`。

#### **4. 图标 (Iconography)**

1.  **图标库**: **必须** 使用 `lucide-react`。它与 Shadcn/ui 完美集成，风格统一。
2.  **使用规范**:
    *   **尺寸**: 图标默认尺寸应为 `w-4 h-4` (16px)。文本旁的图标应与行高对齐。
    *   **粗细**: 保持默认的 `stroke-width` (通常是 2)，以确保视觉一致性。
    *   **颜色**: 图标颜色应使用 `currentColor`，使其能够继承父元素的文字颜色。

#### **5. 动画与过渡 (Animation & Transitions)**

1.  **原则**: 动画应是微妙且有意义的，用于提升用户体验，而非分散注意力。
2.  **实现**: **必须** 使用 Tailwind 的过渡和动画工具类。
    *   **`transition-colors`**: 用于颜色变化的过渡效果（如按钮 hover）。
    *   **`duration-200` / `duration-300`**: 作为标准的过渡时长。
    *   **`ease-in-out`**: 作为标准的缓动函数。

#### **6. 工具与自动化**

为强制执行以上所有规范，项目必须配置以下工具：

1.  **ESLint**:
    *   **必须** 安装并配置 `eslint-plugin-tailwindcss`。
    *   **核心规则**:
        *   `tailwindcss/classnames-order`: 自动对 classname 进行排序。
        *   `tailwindcss/no-custom-classname`: 禁止使用 `tailwind.config.js` 中未定义的 class。
2.  **Prettier**:
    *   **必须** 配置 `.prettierrc` 文件，统一代码格式。
    *   **必须** 安装 `prettier-plugin-tailwindcss` 插件，它会自动根据 `tailwind.config.js` 的配置对类名进行排序。
3.  **Husky & lint-staged**:
    *   **必须** 设置 pre-commit 钩子。
    *   在每次 `git commit` 时，自动对暂存区的文件运行 `prettier --write` 和 `eslint --fix`。

---
**给 AI 编程工具 Augment 的指令总结:**

1.  **初始化**: 在 Vite + React + TS 项目中，立即安装 Tailwind CSS, Shadcn/ui, `lucide-react`, Prettier, ESLint 及相关插件。
2.  **配置文件**: 严格按照本文档中的代码片段内容，生成 `tailwind.config.js`, `globals.css` 和 `.eslintrc.js` 等配置文件。
3.  **组件开发**:
    *   需要新组件时，首先执行 `npx shadcn-ui@latest add [component]`。
    *   若需自定义，则使用 `@/components/ui` 中的基础组件进行组合，并通过 `className` 传入 Tailwind 工具类来定义样式。
    *   所有颜色、字体、间距、圆角等值，都必须通过 Tailwind 的类名（如 `bg-primary`, `text-lg`, `p-4`, `rounded-md`）来使用，决不能硬编码。
4.  **图标使用**: `import { IconName } from 'lucide-react';` 并作为组件使用 `<IconName className="w-4 h-4" />`。
5.  **代码提交**: 提交前，本地环境（通过 Husky）会自动格式化并校验代码，确保所有代码都符合规范。

这份规范是 JobPlus 前端开发的最高准则。请严格遵循。

